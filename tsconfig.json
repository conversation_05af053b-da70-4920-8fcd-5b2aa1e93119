{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022"], "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}