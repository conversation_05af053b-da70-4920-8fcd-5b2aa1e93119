# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Model Context Protocol (MCP) server that provides intelligent code completion, documentation, and analysis for the Fincloud UI component library. It acts as a bridge between IDEs and the Fincloud Angular component library, offering context-aware suggestions and comprehensive documentation.

## Development Commands

### Core Development
- `npm run build` - Compile TypeScript to JavaScript in dist/
- `npm run dev` - Development mode with hot reload using tsx
- `npm start` - Start the production server
- `npm run index` - Manually build the component index

### Code Quality
- `npm run lint` - Run ESLint on TypeScript files
- `npm run lint:fix` - Auto-fix ESLint issues
- `npm test` - Run Jest test suite

### Testing Specific Files
The project uses Jest with TypeScript support. Test files follow patterns:
- `**/__tests__/**/*.+(ts|tsx|js)`
- `**/*.(test|spec).+(ts|tsx|js)`

## Architecture

### Core System Components

The MCP server follows a modular architecture with these key components:

1. **MCP Server Core** (`src/index.ts`): Main server implementation using @modelcontextprotocol/sdk
2. **Component Indexer** (`src/indexers/component-indexer.ts`): Scans and catalogs Fincloud UI components
3. **Completion Provider** (`src/completions/completion-provider.ts`): Provides intelligent code completions
4. **Documentation Generator** (`src/documentation/doc-generator.ts`): Generates component documentation
5. **Code Analyzer** (`src/analysis/code-analyzer.ts`): Analyzes code for best practices and issues
6. **TypeScript Parser** (`src/parsers/typescript-parser.ts`): Parses Angular/TypeScript code
7. **Configuration Manager** (`src/utils/config-manager.ts`): Handles server configuration

### Data Flow Architecture

1. **Indexing Phase**: Scans configured library paths (`libs/ui`, `libs/utils`) to build component registry
2. **Parsing Phase**: Extracts component metadata, APIs, relationships using TypeScript compiler API
3. **Caching Phase**: Stores processed data in `.mcp-cache/` for fast retrieval
4. **Query Phase**: Serves IDE requests with contextual completions and documentation

### MCP Tools Provided

The server exposes 6 main tools via MCP protocol:
- `get_component_completions` - Angular template and TypeScript completions
- `get_component_documentation` - Generate comprehensive component docs
- `analyze_code` - Performance, accessibility, best practices analysis
- `search_components` - Search components by name/functionality
- `get_import_suggestions` - Import path recommendations
- `get_related_components` - Find related/similar components

## Configuration

### Primary Config: `fincloud-ui-mcp.config.json`
The server reads configuration from this file in the project root. Key settings:
- `libraryPath`: Path to Fincloud UI library (default: "../")
- `cacheDir`: Cache directory (default: ".mcp-cache")
- `watchFiles`: Enable file watching for incremental updates
- `analysisRules`: Code analysis configuration
- `completionSettings`: Completion behavior settings

### Alternative Config: `package.json`
Configuration can also be embedded in package.json under `fincloudUiMcp` key.

## Type System

The project uses comprehensive TypeScript schemas defined in `src/types/schema.ts`:
- `ComponentRegistry`: Core component metadata storage
- `UILibrarySchema`: UI component library structure
- `UtilsLibrarySchema`: Utility functions and services
- `CompletionItem`: Code completion data structure
- `AnalysisResult`: Code analysis findings
- `ComponentDocumentation`: Generated documentation format

## Testing and Build Configuration

- **TypeScript**: ESNext modules with ES2022 target, strict type checking enabled
- **Jest**: ESM preset with ts-jest, coverage reporting to `coverage/`
- **Path Mapping**: `@/*` maps to `src/*` for clean imports
- **Build Output**: Compiled to `dist/` with source maps and declarations

## Performance Considerations

- Uses file watching (chokidar) for incremental indexing
- Implements fuzzy search with Fuse.js for fast component lookup
- Caches parsed component metadata with 24-hour TTL
- Lazy loads components on-demand to optimize memory usage

## Dependencies and Integrations

### Core Dependencies
- `@modelcontextprotocol/sdk`: MCP protocol implementation
- `typescript`: TypeScript compiler for parsing Angular code
- `fuse.js`: Fast fuzzy search for component discovery
- `chokidar`: File system watching for incremental updates
- `fast-glob`: Efficient file pattern matching

### Angular Integration
The server is specifically designed for Angular codebases using the Fincloud UI library. It understands:
- Angular component syntax and decorators
- Template binding patterns
- Angular module structure
- Component relationships and dependencies