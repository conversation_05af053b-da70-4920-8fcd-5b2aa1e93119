#!/usr/bin/env node

import { ConfigManager } from '../utils/config-manager.js';
import { FincloudUIIndexer } from '../indexers/component-indexer.js';

async function buildIndex() {
  console.log('Building Fincloud UI component index...');
  
  try {
    const configManager = new ConfigManager();
    await configManager.initialize();
    
    const indexer = new FincloudUIIndexer(configManager);
    await indexer.initialize();
    
    console.log('Index built successfully!');
    
    // Print some statistics
    const componentRegistry = indexer.getComponentRegistry();
    const utilsRegistry = indexer.getUtilsRegistry();
    
    console.log(`\nIndexing Statistics:`);
    console.log(`- Components: ${Object.keys(componentRegistry).length}`);
    console.log(`- Functions: ${Object.keys(utilsRegistry.functions).length}`);
    console.log(`- Services: ${Object.keys(utilsRegistry.services).length}`);
    
    // Show some examples
    console.log(`\nSample Components:`);
    Object.keys(componentRegistry).slice(0, 5).forEach(name => {
      const component = componentRegistry[name];
      console.log(`- ${name} (${component.metadata.selector})`);
    });
    
    console.log(`\nCache saved to: ${configManager.getCacheDir()}`);
    
  } catch (error) {
    console.error('Failed to build index:', error);
    process.exit(1);
  }
}

buildIndex();