#!/usr/bin/env node

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
  ListResourceTemplatesRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";

import { FincloudUIIndexer } from "./indexers/component-indexer.js";
import { CompletionProvider } from "./completions/completion-provider.js";
import { DocumentationGenerator } from "./documentation/doc-generator.js";
import { CodeAnalyzer } from "./analysis/code-analyzer.js";
import { ConfigManager } from "./utils/config-manager.js";

class FincloudUIMCPServer {
  private server: Server;
  private indexer: FincloudUIIndexer;
  private completionProvider: CompletionProvider;
  private docGenerator: DocumentationGenerator;
  private codeAnalyzer: CodeAnalyzer;
  private configManager: ConfigManager;

  constructor() {
    this.server = new Server(
      {
        name: "fincloud-ui-mcp-server",
        version: "1.0.0",
      },
      {
        capabilities: {
          tools: {},
          resources: {},
          resourceTemplates: {},
        },
      }
    );

    this.configManager = new ConfigManager();
    this.indexer = new FincloudUIIndexer(this.configManager);
    this.completionProvider = new CompletionProvider(this.indexer);
    this.docGenerator = new DocumentationGenerator(this.indexer);
    this.codeAnalyzer = new CodeAnalyzer(this.indexer);

    this.setupHandlers();
  }

  private setupHandlers() {
    // List available resource templates (MCP-compliant approach)
    this.server.setRequestHandler(
      ListResourceTemplatesRequestSchema,
      async () => {
        return {
          resourceTemplates: [
            {
              uriTemplate: "fincloud://components/{componentName}",
              name: "Fincloud UI Component",
              description:
                "Individual component with complete metadata, API details, relationships, and examples",
              mimeType: "application/json",
            },
            {
              uriTemplate: "fincloud://utils/{utilityName}",
              name: "Fincloud Utility",
              description:
                "Individual utility function or service with documentation and usage examples",
              mimeType: "application/json",
            },
          ],
        };
      }
    );

    // List available resources (simplified - no individual component enumeration)
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      return {
        resources: [
          {
            uri: "fincloud://components",
            name: "All Fincloud UI Components",
            description:
              "Complete registry of all UI components with metadata, API details, and relationships",
            mimeType: "application/json",
          },
          {
            uri: "fincloud://components/summary",
            name: "Components Summary",
            description:
              "Lightweight summary of all components (names, categories, selectors only)",
            mimeType: "application/json",
          },
          {
            uri: "fincloud://utils",
            name: "All Fincloud Utils",
            description:
              "Complete registry of all utility functions and services",
            mimeType: "application/json",
          },
          {
            uri: "fincloud://examples",
            name: "Code Examples",
            description: "Code examples for components and utilities",
            mimeType: "application/json",
          },
        ],
      };
    });

    // Read resource contents
    this.server.setRequestHandler(
      ReadResourceRequestSchema,
      async (request) => {
        const uri = request.params.uri;

        // Handle static resources FIRST (before template matching)
        if (uri === "fincloud://components") {
          // Return complete component registry
          const componentRegistry = this.indexer.getComponentRegistry();

          return {
            contents: [
              {
                uri,
                mimeType: "application/json",
                text: JSON.stringify(componentRegistry, null, 2),
              },
            ],
          };
        }

        if (uri === "fincloud://components/summary") {
          const summary = this.indexer.getComponentsSummary();
          return {
            contents: [
              {
                uri,
                mimeType: "application/json",
                text: JSON.stringify(summary, null, 2),
              },
            ],
          };
        }

        // Handle resource template: fincloud://components/{componentName}
        const componentMatch = uri.match(/^fincloud:\/\/components\/([^\/]+)$/);
        if (componentMatch) {
          const componentName = componentMatch[1];
          return await this.handleComponentResource(componentName, uri);
        }

        // Handle resource template: fincloud://utils/{utilityName}
        const utilityMatch = uri.match(/^fincloud:\/\/utils\/([^\/]+)$/);
        if (utilityMatch) {
          const utilityName = utilityMatch[1];
          return await this.handleUtilityResource(utilityName, uri);
        }

        if (uri === "fincloud://utils") {
          return {
            contents: [
              {
                uri,
                mimeType: "application/json",
                text: JSON.stringify(this.indexer.getUtilsRegistry(), null, 2),
              },
            ],
          };
        }

        if (uri === "fincloud://examples") {
          return {
            contents: [
              {
                uri,
                mimeType: "application/json",
                text: JSON.stringify(this.indexer.getExamples(), null, 2),
              },
            ],
          };
        }

        throw new Error(`Resource not found: ${uri}`);
      }
    );

    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: "get_component_completions",
            description:
              "Get code completion suggestions for Angular templates and TypeScript",
            inputSchema: {
              type: "object",
              properties: {
                context: {
                  type: "string",
                  description: "The code context where completion is requested",
                },
                position: {
                  type: "object",
                  properties: {
                    line: { type: "number" },
                    character: { type: "number" },
                  },
                  required: ["line", "character"],
                },
                filePath: {
                  type: "string",
                  description: "Path to the file being edited",
                },
              },
              required: ["context", "position"],
            },
          },
          {
            name: "get_component_documentation",
            description: "Generate comprehensive documentation for a component",
            inputSchema: {
              type: "object",
              properties: {
                componentName: {
                  type: "string",
                  description: "Name of the component to document",
                },
                includeExamples: {
                  type: "boolean",
                  description: "Whether to include code examples",
                  default: true,
                },
              },
              required: ["componentName"],
            },
          },
          {
            name: "analyze_code",
            description:
              "Analyze code for Fincloud UI component usage, deprecated components, and Fincloud-specific best practices",
            inputSchema: {
              type: "object",
              properties: {
                filePath: {
                  type: "string",
                  description: "Path to the file to analyze",
                },
                code: {
                  type: "string",
                  description:
                    "Code content to analyze (optional if filePath provided)",
                },
              },
            },
          },
          {
            name: "search_components",
            description:
              "Search for components by name, functionality, or usage",
            inputSchema: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "Search query",
                },
                category: {
                  type: "string",
                  enum: [
                    "form",
                    "layout",
                    "navigation",
                    "data-display",
                    "feedback",
                    "all",
                  ],
                  description: "Component category to search within",
                  default: "all",
                },
                includeUtils: {
                  type: "boolean",
                  description: "Whether to include utility functions in search",
                  default: false,
                },
              },
              required: ["query"],
            },
          },
          {
            name: "get_import_suggestions",
            description:
              "Get import path suggestions for components and utilities",
            inputSchema: {
              type: "object",
              properties: {
                symbol: {
                  type: "string",
                  description: "Symbol name to find import for",
                },
                currentFilePath: {
                  type: "string",
                  description: "Path of the current file",
                },
              },
              required: ["symbol"],
            },
          },
          {
            name: "get_related_components",
            description:
              "Find components related to or similar to a given component",
            inputSchema: {
              type: "object",
              properties: {
                componentName: {
                  type: "string",
                  description:
                    "Name of the component to find related components for",
                },
                relationshipType: {
                  type: "string",
                  enum: ["similar", "dependencies", "dependents", "all"],
                  description: "Type of relationship to search for",
                  default: "all",
                },
              },
              required: ["componentName"],
            },
          },
        ],
      };
    });

    // Handle tool execution
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case "get_component_completions": {
          const { context, position, filePath } = args as {
            context: string;
            position: { line: number; character: number };
            filePath?: string;
          };
          const completions = await this.completionProvider.getCompletions({
            context,
            position,
            filePath: filePath || "unknown",
          });
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(completions, null, 2),
              },
            ],
          };
        }

        case "get_component_documentation": {
          const { componentName, includeExamples = true } = args as {
            componentName: string;
            includeExamples?: boolean;
          };
          const documentation = await this.docGenerator.generateDocumentation(
            componentName,
            { includeExamples }
          );
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(documentation, null, 2),
              },
            ],
          };
        }

        case "analyze_code": {
          const { filePath, code } = args as {
            filePath?: string;
            code?: string;
          };
          const analysis = await this.codeAnalyzer.analyzeCode({
            filePath,
            code,
          });
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(analysis, null, 2),
              },
            ],
          };
        }

        case "search_components": {
          const {
            query,
            category = "all",
            includeUtils = false,
          } = args as {
            query: string;
            category?: string;
            includeUtils?: boolean;
          };
          const results = await this.indexer.searchComponents(query, {
            category,
            includeUtils,
          });
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(results, null, 2),
              },
            ],
          };
        }

        case "get_import_suggestions": {
          const { symbol } = args as {
            symbol: string;
          };
          const suggestions =
            await this.completionProvider.getImportSuggestions(symbol);
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(suggestions, null, 2),
              },
            ],
          };
        }

        case "get_related_components": {
          const { componentName, relationshipType = "all" } = args as {
            componentName: string;
            relationshipType?: string;
          };
          const related = await this.indexer.getRelatedComponents(
            componentName,
            relationshipType
          );
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(related, null, 2),
              },
            ],
          };
        }

        default:
          throw new Error(`Tool not found: ${name}`);
      }
    });
  }

  // Helper method to handle component resource template requests
  private async handleComponentResource(componentName: string, uri: string) {
    const component = this.indexer.getComponentMetadata(componentName);

    if (!component) {
      // Use proper JSON-RPC error code per MCP specification
      const error = new Error(`Component not found: ${componentName}`);
      (error as any).code = -32002; // Resource not found error code
      (error as any).data = { uri };
      throw error;
    }

    const fullComponent = this.indexer.getComponentRegistry()[componentName];
    return {
      contents: [
        {
          uri,
          mimeType: "application/json",
          text: JSON.stringify(fullComponent, null, 2),
        },
      ],
    };
  }

  // Helper method to handle utility resource template requests
  private async handleUtilityResource(utilityName: string, uri: string) {
    const utilsRegistry = this.indexer.getUtilsRegistry();

    // Check if it's a function
    if (utilsRegistry.functions[utilityName]) {
      return {
        contents: [
          {
            uri,
            mimeType: "application/json",
            text: JSON.stringify(utilsRegistry.functions[utilityName], null, 2),
          },
        ],
      };
    }

    // Check if it's a service
    if (utilsRegistry.services[utilityName]) {
      return {
        contents: [
          {
            uri,
            mimeType: "application/json",
            text: JSON.stringify(utilsRegistry.services[utilityName], null, 2),
          },
        ],
      };
    }

    // Utility not found
    const error = new Error(`Utility not found: ${utilityName}`);
    (error as any).code = -32002; // Resource not found error code
    (error as any).data = { uri };
    throw error;
  }

  async run() {
    try {
      // Initialize the indexer
      await this.indexer.initialize();

      const transport = new StdioServerTransport();
      await this.server.connect(transport);
    } catch (error) {
      console.error("Error during server startup:");
      console.error("Error name:", (error as any)?.name || "Unknown");
      console.error("Error message:", (error as any)?.message || "No message");
      console.error("Error stack:", (error as any)?.stack || "No stack");
      throw error; // Re-throw to be caught by the outer catch
    }
  }
}

const server = new FincloudUIMCPServer();
server.run().catch((error) => {
  console.error("=== FATAL SERVER ERROR ===");
  console.error("Error type:", typeof error);
  console.error("Error name:", error?.name || "Unknown");
  console.error("Error message:", error?.message || "No message");
  console.error("Error code:", error?.code || "No code");
  console.error("Full error object:", JSON.stringify(error, null, 2));
  console.error("Error stack:");
  console.error(error?.stack || "No stack trace available");
  console.error("=== END ERROR DETAILS ===");
  process.exit(1);
});
