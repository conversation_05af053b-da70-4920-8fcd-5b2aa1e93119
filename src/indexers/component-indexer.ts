import { readdir, readFile, stat, mkdir, writeFile, access } from "fs/promises";
import { join, resolve } from "path";
import { existsSync } from "fs";
import { watch } from "chokidar";
import { createHash } from "crypto";
import Fuse from "fuse.js";

import {
  FincloudLibrarySchema,
  ComponentRegistry,
  UtilsLibrarySchema,
  FunctionRegistry,
  ServiceRegistry,
  ComponentMetadata,
  ComponentRelationships,
  CodeExample,
} from "../types/schema.js";
import {
  TypeScriptParser,
  ParsedComponent,
  ParsedService,
  ParsedFunction,
} from "../parsers/typescript-parser.js";
import { ConfigManager } from "../utils/config-manager.js";

export interface SearchOptions {
  category?: string;
  includeUtils?: boolean;
  limit?: number;
}

export interface SearchResult {
  type: "component" | "service" | "function" | "pipe";
  name: string;
  description: string;
  category: string;
  exportPath: string;
  score: number;
}

export interface ComponentSummary {
  name: string;
  selector: string;
  category: string;
  exportPath: string;
  deprecated?: boolean;
}

export interface ComponentsSummaryResult {
  components: ComponentSummary[];
}

export class FincloudUIIndexer {
  private schema: FincloudLibrarySchema;
  private parser: TypeScriptParser;
  private configManager: ConfigManager;
  private componentSearch: Fuse<any>;
  private utilsSearch: Fuse<any>;
  private isInitialized = false;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.parser = new TypeScriptParser(configManager.getTsConfigPath());

    this.schema = {
      version: "0.0.663",
      lastUpdated: new Date(),
      libraries: {
        ui: {
          components: {},
          modules: {},
          assets: {},
          styles: {},
        },
        utils: {
          functions: {},
          services: {},
          pipes: {},
          decorators: {},
          operators: {},
        },
      },
    };

    this.componentSearch = new Fuse([], {
      keys: ["name", "selector", "documentation", "category", "tags"],
      threshold: 0.3,
      includeScore: true,
    });

    this.utilsSearch = new Fuse([], {
      keys: ["name", "documentation", "category"],
      threshold: 0.3,
      includeScore: true,
    });
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    await this.configManager.initialize();
    await this.ensureCacheDirectory();

    // Try to load from cache first
    const cacheLoaded = await this.loadFromCache();

    if (!cacheLoaded) {
      // Start building index in background - don't wait for it
      this.buildIndexInBackground();
    } else {
      this.setupSearchIndices();
    }

    if (this.configManager.shouldWatchFiles()) {
      this.setupFileWatcher();
    }

    this.isInitialized = true;
  }

  private async buildIndexInBackground(): Promise<void> {
    try {
      await this.buildIndex();
      await this.saveToCache();
      this.setupSearchIndices();
    } catch (error) {
      console.error("Background indexing failed:", error);
    }
  }

  private async ensureCacheDirectory(): Promise<void> {
    const cacheDir = this.configManager.getCacheDir();
    try {
      await access(cacheDir);
    } catch {
      await mkdir(cacheDir, { recursive: true });
    }
  }

  private async loadFromCache(): Promise<boolean> {
    try {
      const libraryPath = this.configManager.getLibraryPath();
      const libraryHash = this.generateLibraryHash(libraryPath);
      const cachePath = join(
        this.configManager.getCacheDir(),
        `component-index-${libraryHash}.json`
      );

      const cacheContent = await readFile(cachePath, "utf-8");
      const cachedSchema = JSON.parse(cacheContent);

      // Check if cache is still valid
      const cacheAge =
        Date.now() - new Date(cachedSchema.lastUpdated).getTime();
      const maxCacheAge = 24 * 60 * 60 * 1000; // 24 hours

      // Check if cache has actual content
      const hasComponents =
        Object.keys(cachedSchema.libraries?.ui?.components || {}).length > 0;
      const hasUtils =
        Object.keys(cachedSchema.libraries?.utils?.functions || {}).length >
          0 ||
        Object.keys(cachedSchema.libraries?.utils?.services || {}).length > 0;

      // Check if library has been modified since cache was created
      const libraryModified = await this.isLibraryModifiedSinceCache(
        libraryPath,
        new Date(cachedSchema.lastUpdated)
      );

      if (
        cacheAge < maxCacheAge &&
        (hasComponents || hasUtils) &&
        !libraryModified
      ) {
        this.schema = cachedSchema;
        return true;
      }
    } catch (error) {
      // Cache miss is expected on first run - don't log as error
    }

    return false;
  }

  private async saveToCache(): Promise<void> {
    try {
      const libraryPath = this.configManager.getLibraryPath();
      const libraryHash = this.generateLibraryHash(libraryPath);
      const cachePath = join(
        this.configManager.getCacheDir(),
        `component-index-${libraryHash}.json`
      );
      await writeFile(cachePath, JSON.stringify(this.schema, null, 2));
    } catch (error) {
      console.error("Failed to save to cache:", error);
    }
  }

  private generateLibraryHash(libraryPath: string): string {
    // Create a hash based on the library path to namespace caches
    return createHash("md5").update(libraryPath).digest("hex").substring(0, 8);
  }

  private async isLibraryModifiedSinceCache(
    libraryPath: string,
    cacheDate: Date
  ): Promise<boolean> {
    try {
      // Check if any key directories have been modified since cache was created
      const keyPaths = [
        join(libraryPath, "libs", "ui"),
        join(libraryPath, "libs", "utils"),
        join(libraryPath, "package.json"),
        join(libraryPath, "package-lock.json"),
      ];

      for (const path of keyPaths) {
        if (existsSync(path)) {
          const stats = await stat(path);
          if (stats.mtime > cacheDate) {
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      console.error("Error checking library modification time:", error);
      // If we can't determine, assume it's modified to be safe
      return true;
    }
  }

  private async buildIndex(): Promise<void> {
    // Index UI components
    await this.indexUIComponents();

    // Index utilities
    await this.indexUtilities();

    // Build relationships
    await this.buildRelationships();

    // Extract examples
    await this.extractExamples();

    this.schema.lastUpdated = new Date();
  }

  private async indexUIComponents(): Promise<void> {
    const uiPath = this.configManager.getUILibPath();
    const componentDirs = await this.getComponentDirectories(uiPath);

    for (const componentDir of componentDirs) {
      await this.indexComponent(componentDir);
    }
  }

  private async getComponentDirectories(basePath: string): Promise<string[]> {
    const entries = await readdir(basePath, { withFileTypes: true });
    const componentDirs: string[] = [];

    for (const entry of entries) {
      if (
        entry.isDirectory() &&
        !entry.name.startsWith(".") &&
        entry.name !== "assets" &&
        entry.name !== "styles"
      ) {
        componentDirs.push(join(basePath, entry.name));
      }
    }

    return componentDirs;
  }

  private async indexComponent(componentDir: string): Promise<void> {
    try {
      // Find the main component file
      const componentFiles = await this.findComponentFiles(componentDir);

      for (const componentFile of componentFiles) {
        const parsedComponent = await this.parser.parseComponent(componentFile);

        if (parsedComponent) {
          const componentName = parsedComponent.metadata.name;

          this.schema.libraries.ui.components[componentName] = {
            metadata: parsedComponent.metadata,
            api: parsedComponent.api,
            relationships: {
              dependencies: parsedComponent.metadata.dependencies,
              dependents: [],
              relatedComponents: [],
              alternatives: [],
              commonlyUsedWith: [],
            },
            examples: [],
            tests: [],
          };
        }
      }
    } catch (error) {
      console.error(`Failed to index component in ${componentDir}:`, error);
    }
  }

  private async findComponentFiles(dir: string): Promise<string[]> {
    const files: string[] = [];

    const searchDir = async (currentDir: string): Promise<void> => {
      const entries = await readdir(currentDir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = join(currentDir, entry.name);

        if (entry.isDirectory() && entry.name !== "node_modules") {
          await searchDir(fullPath);
        } else if (
          entry.isFile() &&
          entry.name.endsWith(".component.ts") &&
          !entry.name.endsWith(".spec.ts")
        ) {
          files.push(fullPath);
        }
      }
    };

    await searchDir(dir);
    return files;
  }

  private async indexUtilities(): Promise<void> {
    if (!this.configManager.hasUtilsLib()) {
      console.log("Utils library not found, skipping utilities indexing");
      return;
    }

    const utilsPath = this.configManager.getUtilsLibPath();
    const utilDirs = await this.getComponentDirectories(utilsPath);

    for (const utilDir of utilDirs) {
      await this.indexUtilityDirectory(utilDir);
    }
  }

  private async indexUtilityDirectory(utilDir: string): Promise<void> {
    try {
      const files = await this.findTypeScriptFiles(utilDir);

      for (const file of files) {
        // Try to parse as service
        const service = await this.parser.parseService(file);
        if (service) {
          this.schema.libraries.utils.services[service.className] = {
            className: service.className,
            filePath: file,
            exportPath: this.getExportPathForFile(file),
            methods: service.methods,
            properties: service.properties,
            documentation: service.documentation,
          };
          continue;
        }

        // Parse functions from the file
        await this.indexFunctionsInFile(file);
      }
    } catch (error) {
      console.error(`Failed to index utility directory ${utilDir}:`, error);
    }
  }

  private async findTypeScriptFiles(dir: string): Promise<string[]> {
    const files: string[] = [];

    const searchDir = async (currentDir: string): Promise<void> => {
      const entries = await readdir(currentDir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = join(currentDir, entry.name);

        if (entry.isDirectory() && entry.name !== "node_modules") {
          await searchDir(fullPath);
        } else if (
          entry.isFile() &&
          entry.name.endsWith(".ts") &&
          !entry.name.endsWith(".spec.ts") &&
          !entry.name.endsWith(".d.ts")
        ) {
          files.push(fullPath);
        }
      }
    };

    await searchDir(dir);
    return files;
  }

  private async indexFunctionsInFile(filePath: string): Promise<void> {
    try {
      const content = await readFile(filePath, "utf-8");
      const functionNames = this.extractFunctionNames(content);

      for (const functionName of functionNames) {
        const parsedFunction = await this.parser.parseFunction(
          filePath,
          functionName
        );

        if (parsedFunction) {
          this.schema.libraries.utils.functions[functionName] = {
            signature: parsedFunction,
            usage: [],
            relatedFunctions: [],
            category: this.categorizeFunctionByPath(filePath),
            filePath,
            exportPath: this.getExportPathForFile(filePath),
          };
        }
      }
    } catch (error) {
      console.error(`Failed to index functions in ${filePath}:`, error);
    }
  }

  private extractFunctionNames(content: string): string[] {
    const functionRegex = /export\s+(?:function\s+(\w+)|const\s+(\w+)\s*=)/g;
    const names: string[] = [];
    let match;

    while ((match = functionRegex.exec(content)) !== null) {
      const name = match[1] || match[2];
      if (name) {
        names.push(name);
      }
    }

    return names;
  }

  private getExportPathForFile(filePath: string): string {
    const match = filePath.match(/libs\/(ui|utils)\/([^/]+)/);
    if (match) {
      return `@fincloud/${match[1]}/${match[2]}`;
    }
    return "";
  }

  private categorizeFunctionByPath(filePath: string): string {
    if (filePath.includes("angular-functions")) return "angular";
    if (filePath.includes("rxjs-operators")) return "rxjs";
    if (filePath.includes("functions")) return "utility";
    if (filePath.includes("pipes")) return "pipes";
    if (filePath.includes("decorators")) return "decorators";
    return "other";
  }

  private async buildRelationships(): Promise<void> {
    const components = this.schema.libraries.ui.components;

    for (const [componentName, componentData] of Object.entries(components)) {
      // Build dependency relationships
      for (const dependency of componentData.metadata.dependencies) {
        if (dependency.startsWith("@fincloud/ui/")) {
          const depComponentName = this.findComponentByExportPath(dependency);
          if (depComponentName && components[depComponentName]) {
            components[depComponentName].relationships.dependents.push(
              componentName
            );
          }
        }
      }

      // Find related components based on category and common usage patterns
      componentData.relationships.relatedComponents =
        this.findRelatedComponentsByCategory(
          componentData.metadata.category,
          componentName
        );
    }
  }

  private findComponentByExportPath(exportPath: string): string | null {
    for (const [name, data] of Object.entries(
      this.schema.libraries.ui.components
    )) {
      if (data.metadata.exportPath === exportPath) {
        return name;
      }
    }
    return null;
  }

  private findRelatedComponentsByCategory(
    category: string,
    excludeName: string
  ): string[] {
    return Object.entries(this.schema.libraries.ui.components)
      .filter(
        ([name, data]) =>
          name !== excludeName && data.metadata.category === category
      )
      .map(([name]) => name)
      .slice(0, 5); // Limit to 5 related components
  }

  private async extractExamples(): Promise<void> {
    // This would scan for .stories.ts files and extract examples
    for (const [componentName, componentData] of Object.entries(
      this.schema.libraries.ui.components
    )) {
      const examplePath = componentData.metadata.filePath.replace(
        ".component.ts",
        ".component.stories.ts"
      );

      if (existsSync(examplePath)) {
        try {
          const storyContent = await readFile(examplePath, "utf-8");
          const examples = this.extractExamplesFromStories(
            storyContent,
            componentName
          );
          componentData.examples = examples;
        } catch (error) {
          console.error(
            `Failed to extract examples for ${componentName}:`,
            error
          );
        }
      }
    }
  }

  private extractExamplesFromStories(
    content: string,
    componentName: string
  ): CodeExample[] {
    const examples: CodeExample[] = [];

    // Look for story exports - use simpler approach
    const storyRegex = /export\s+const\s+(\w+)\s*:\s*\w+\s*=\s*{/g;
    let match;

    while ((match = storyRegex.exec(content)) !== null) {
      const storyName = match[1];

      if (storyName !== "default") {
        // Create a basic example with extracted template if possible
        const template = this.extractTemplateFromFullContent(
          content,
          storyName
        );
        const htmlCode = this.generateHtmlTemplate(componentName, template);

        examples.push({
          title: `${componentName} - ${storyName}`,
          description: `Example usage of ${componentName} component - ${storyName} variant`,
          code: {
            html: htmlCode,
          },
        });
      }
    }

    return examples;
  }

  private extractTemplateFromFullContent(
    content: string,
    storyName: string
  ): string {
    // Look for inline templates in the story
    const storyPattern = new RegExp(
      `export\\s+const\\s+${storyName}[\\s\\S]*?template:\\s*\`([\\s\\S]*?)\``,
      "g"
    );
    const match = storyPattern.exec(content);

    if (match) {
      return match[1].trim();
    }

    // Look for render function templates
    const renderPattern = new RegExp(
      `export\\s+const\\s+${storyName}[\\s\\S]*?render:[\\s\\S]*?template:\\s*\`([\\s\\S]*?)\``,
      "g"
    );
    const renderMatch = renderPattern.exec(content);

    if (renderMatch) {
      return renderMatch[1].trim();
    }

    // Look for renderTemplate function calls
    const renderTemplatePattern = new RegExp(
      `const\\s+renderTemplate\\s*=[\\s\\S]*?template:\\s*\`([\\s\\S]*?)\``,
      "g"
    );
    const templateMatch = renderTemplatePattern.exec(content);

    if (templateMatch) {
      return templateMatch[1].trim();
    }

    return "";
  }

  private generateHtmlTemplate(
    componentName: string,
    template: string
  ): string {
    const componentSelector = this.getComponentSelector(componentName);
    const finalTemplate =
      template || `<${componentSelector}></${componentSelector}>`;

    // Return only the HTML template content, preserving Angular directives and syntax
    return finalTemplate.trim();
  }

  private getComponentSelector(componentName: string): string {
    // Convert FinButtonComponent to fin-button
    return componentName
      .replace(/^Fin/, "")
      .replace(/Component$/, "")
      .replace(/([A-Z])/g, "-$1")
      .toLowerCase()
      .substring(1);
  }

  private setupSearchIndices(): void {
    // Setup component search index
    const componentItems = Object.entries(
      this.schema.libraries.ui.components
    ).map(([name, data]) => ({
      type: "component",
      name,
      selector: data.api.selector,
      documentation: data.metadata.documentation,
      category: data.metadata.category,
      tags: data.metadata.tags,
      exportPath: data.metadata.exportPath,
    }));

    this.componentSearch = new Fuse(componentItems, {
      keys: ["name", "selector", "documentation", "category", "tags"],
      threshold: 0.3,
      includeScore: true,
    });

    // Setup utils search index
    const utilItems = [
      ...Object.entries(this.schema.libraries.utils.functions).map(
        ([name, data]) => ({
          type: "function",
          name,
          documentation: data.signature.documentation,
          category: data.category,
          exportPath: data.exportPath,
        })
      ),
      ...Object.entries(this.schema.libraries.utils.services).map(
        ([name, data]) => ({
          type: "service",
          name,
          documentation: data.documentation,
          category: "service",
          exportPath: data.exportPath,
        })
      ),
    ];

    this.utilsSearch = new Fuse(utilItems, {
      keys: ["name", "documentation", "category"],
      threshold: 0.3,
      includeScore: true,
    });
  }

  private setupFileWatcher(): void {
    const watchPaths = [
      this.configManager.getUILibPath(),
      this.configManager.getUtilsLibPath(),
    ];

    const watcher = watch(watchPaths, {
      ignored: /node_modules/,
      persistent: true,
    });

    watcher.on("change", async (path) => {
      // In a full implementation, you'd incrementally update the index
      // For now, we'll just mark that a rebuild is needed
    });

    watcher.on("add", async (path) => {
      // File added - could trigger incremental indexing
    });

    watcher.on("unlink", async (path) => {
      // File removed - could trigger cache cleanup
    });
  }

  // Public API methods

  getComponentRegistry(): ComponentRegistry {
    return this.schema?.libraries?.ui?.components || {};
  }

  getUtilsRegistry(): UtilsLibrarySchema {
    return (
      this.schema?.libraries?.utils || {
        functions: {},
        services: {},
        pipes: {},
        decorators: {},
        operators: {},
      }
    );
  }

  getExamples(): CodeExample[] {
    const examples: CodeExample[] = [];

    if (!this.schema?.libraries?.ui?.components) {
      return examples;
    }

    for (const componentData of Object.values(
      this.schema.libraries.ui.components
    )) {
      examples.push(...componentData.examples);
    }

    return examples;
  }

  async searchComponents(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    const { category = "all", includeUtils = false, limit = 20 } = options;
    const results: SearchResult[] = [];

    // Return empty results if index is not ready yet
    if (!this.componentSearch || !this.schema?.libraries?.ui?.components) {
      return [];
    }

    // Search components
    const componentResults = this.componentSearch.search(query);
    for (const result of componentResults) {
      if (category === "all" || result.item.category === category) {
        results.push({
          type: "component",
          name: result.item.name,
          description:
            result.item.documentation || `${result.item.name} component`,
          category: result.item.category,
          exportPath: result.item.exportPath,
          score: result.score || 0,
        });
      }
    }

    // Search utilities if requested
    if (includeUtils) {
      const utilResults = this.utilsSearch.search(query);
      for (const result of utilResults) {
        results.push({
          type: result.item.type,
          name: result.item.name,
          description:
            result.item.documentation ||
            `${result.item.name} ${result.item.type}`,
          category: result.item.category,
          exportPath: result.item.exportPath,
          score: result.score || 0,
        });
      }
    }

    // Sort by score and limit results
    return results.sort((a, b) => a.score - b.score).slice(0, limit);
  }

  async getRelatedComponents(
    componentName: string,
    relationType: string
  ): Promise<string[]> {
    const component = this.schema.libraries.ui.components[componentName];

    if (!component) {
      return [];
    }

    const relationships = component.relationships;

    switch (relationType) {
      case "dependencies":
        return relationships.dependencies;
      case "dependents":
        return relationships.dependents;
      case "similar":
        return relationships.alternatives;
      case "commonly-used-with":
        return relationships.commonlyUsedWith;
      default:
        return relationships.relatedComponents;
    }
  }

  getComponentMetadata(componentName: string): ComponentMetadata | null {
    const component = this.schema.libraries.ui.components[componentName];
    return component ? component.metadata : null;
  }

  getAllComponentNames(): string[] {
    return Object.keys(this.schema.libraries.ui.components);
  }

  getAllUtilityNames(): string[] {
    return [
      ...Object.keys(this.schema.libraries.utils.functions),
      ...Object.keys(this.schema.libraries.utils.services),
    ];
  }

  // MCP Resource: Components Summary
  getComponentsSummary(): ComponentsSummaryResult {
    const allComponents = this.schema.libraries.ui.components;
    const components: ComponentSummary[] = [];

    // Build complete list of components
    Object.entries(allComponents).forEach(([name, component]) => {
      const componentSummary: ComponentSummary = {
        name,
        selector: component.metadata.selector,
        category: component.metadata.category,
        exportPath: component.metadata.exportPath,
        deprecated: component.metadata.deprecated,
      };

      components.push(componentSummary);
    });

    return {
      components,
    };
  }
}
