import {
  CompletionItem,
  CompletionItemKind,
  ImportSuggestion,
  Position,
} from "../types/schema.js";
import { FincloudUIIndexer } from "../indexers/component-indexer.js";

export interface CompletionContext {
  context: string;
  position: Position;
  filePath: string;
}

export interface FincloudTemplateContext {
  type: "fincloud-element" | "fincloud-attribute" | "fincloud-import";
  element?: string;
}

export class CompletionProvider {
  private indexer: FincloudUIIndexer;

  constructor(indexer: FincloudUIIndexer) {
    this.indexer = indexer;
  }

  async getCompletions(context: CompletionContext): Promise<CompletionItem[]> {
    const { context: code, position, filePath } = context;

    // Determine if we're in a TypeScript file or template
    if (
      filePath.endsWith(".component.html") ||
      this.isInTemplate(code, position)
    ) {
      return this.getFincloudTemplateCompletions(code, position);
    } else if (filePath.endsWith(".ts")) {
      return this.getFincloudImportCompletions(code, position);
    }

    return [];
  }

  private getFincloudTemplateCompletions(
    template: string,
    position: Position
  ): CompletionItem[] {
    const templateContext = this.analyzeFincloudTemplateContext(
      template,
      position
    );

    switch (templateContext.type) {
      case "fincloud-element":
        return this.getFincloudElementCompletions();
      case "fincloud-attribute":
        return this.getFincloudAttributeCompletions(
          templateContext.element || ""
        );
      default:
        return [];
    }
  }

  private getFincloudImportCompletions(
    code: string,
    position: Position
  ): CompletionItem[] {
    const line = this.getLineAtPosition(code, position);
    const currentWord = this.getCurrentWord(line, position.character);

    // Only handle Fincloud import statement completions
    if (this.isInImportStatement(line)) {
      return this.getFincloudImportPathCompletions(currentWord);
    }

    return [];
  }

  private getFincloudElementCompletions(): CompletionItem[] {
    const components = this.indexer.getComponentRegistry();
    const completions: CompletionItem[] = [];

    for (const [name, componentData] of Object.entries(components)) {
      const selector = componentData.api.selector;

      // Only include Fincloud components (those starting with 'fin-')
      if (selector.startsWith("fin-")) {
        completions.push({
          label: selector,
          kind: CompletionItemKind.Class,
          detail: `Fincloud UI Component: ${name}`,
          documentation: componentData.metadata.documentation,
          insertText: this.generateFincloudComponentSnippet(componentData.api),
          sortText: `0_${selector}`,
        });
      }
    }

    return completions;
  }

  private getFincloudAttributeCompletions(
    elementName: string
  ): CompletionItem[] {
    const completions: CompletionItem[] = [];
    const component = this.findFincloudComponentBySelector(elementName);

    if (component) {
      // Add Fincloud component input properties
      for (const input of component.api.inputs) {
        completions.push({
          label: input.name,
          kind: CompletionItemKind.Property,
          detail: `Fincloud Input: ${input.type}`,
          documentation: input.documentation,
          insertText: input.required
            ? `${input.name}="$1"`
            : `[${input.name}]="$1"`,
        });
      }

      // Add Fincloud component output events
      for (const output of component.api.outputs) {
        completions.push({
          label: `(${output.name})`,
          kind: CompletionItemKind.Event,
          detail: `Fincloud Output: ${output.type}`,
          documentation: output.documentation,
          insertText: `(${output.name})="$1"`,
        });
      }
    }

    return completions;
  }

  private getFincloudImportPathCompletions(
    currentWord: string
  ): CompletionItem[] {
    const completions: CompletionItem[] = [];

    // Only handle Fincloud UI import completions
    if (
      currentWord.includes("@fincloud/ui") ||
      currentWord.includes("fincloud")
    ) {
      const components = this.indexer.getComponentRegistry();

      for (const [componentName, componentData] of Object.entries(components)) {
        // Only suggest Fincloud components
        if (componentData.api.selector.startsWith("fin-")) {
          completions.push({
            label: componentData.metadata.exportPath,
            kind: CompletionItemKind.Module,
            detail: `Import Fincloud ${componentName}`,
            documentation: componentData.metadata.documentation,
            insertText: componentData.metadata.exportPath,
          });
        }
      }

      // Add common Fincloud import paths
      const commonFincloudPaths = [
        "@fincloud/ui/components",
        "@fincloud/ui/utils",
        "@fincloud/ui/services",
        "@fincloud/ui/pipes",
      ];

      for (const path of commonFincloudPaths) {
        if (path.includes(currentWord) || currentWord.includes("fincloud")) {
          completions.push({
            label: path,
            kind: CompletionItemKind.Module,
            detail: "Fincloud UI module",
            insertText: path,
          });
        }
      }
    }

    return completions;
  }

  async getImportSuggestions(symbol: string): Promise<ImportSuggestion[]> {
    const suggestions: ImportSuggestion[] = [];

    // Only search Fincloud components
    const components = this.indexer.getComponentRegistry();
    for (const [name, componentData] of Object.entries(components)) {
      // Only suggest Fincloud components
      if (
        componentData.api.selector.startsWith("fin-") &&
        (name.includes(symbol) || componentData.api.selector.includes(symbol))
      ) {
        suggestions.push({
          importPath: componentData.metadata.exportPath,
          namedImports: [name],
          isDefault: false,
          confidence: this.calculateFincloudConfidence(name, symbol),
        });
      }
    }

    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  // Helper methods

  private isInTemplate(code: string, position: Position): boolean {
    // Check if cursor is within template literal or template file
    const line = this.getLineAtPosition(code, position);
    return line.includes("template:") || line.includes("`");
  }

  private analyzeFincloudTemplateContext(
    template: string,
    position: Position
  ): FincloudTemplateContext {
    const lines = template.split("\n");
    const currentLine = lines[position.line] || "";
    const beforeCursor = currentLine.substring(0, position.character);

    // Check if we're in a Fincloud element tag
    if (beforeCursor.includes("<") && !beforeCursor.includes(">")) {
      if (beforeCursor.endsWith(" ") || beforeCursor.endsWith("\t")) {
        // We're in attribute position for a Fincloud component
        const fincloudElementMatch = beforeCursor.match(/<(fin-[\w-]*)/);
        if (fincloudElementMatch) {
          return {
            type: "fincloud-attribute",
            element: fincloudElementMatch[1],
          };
        }
      } else {
        // We're typing element name - check if it's a Fincloud component
        if (beforeCursor.includes("<fin-") || beforeCursor.includes("fin-")) {
          return { type: "fincloud-element" };
        }
      }
    }

    // Check if we're in an import statement for Fincloud
    if (beforeCursor.includes("from") && beforeCursor.includes("fincloud")) {
      return { type: "fincloud-import" };
    }

    // Default: assume element completion for Fincloud
    return { type: "fincloud-element" };
  }

  private getLineAtPosition(code: string, position: Position): string {
    const lines = code.split("\n");
    return lines[position.line] || "";
  }

  private getCurrentWord(line: string, character: number): string {
    const beforeCursor = line.substring(0, character);
    const match = beforeCursor.match(/[\w-@/]*$/);
    return match ? match[0] : "";
  }

  private isInImportStatement(line: string): boolean {
    return line.trim().startsWith("import") && line.includes("from");
  }

  private findFincloudComponentBySelector(selector: string): any {
    const components = this.indexer.getComponentRegistry();

    for (const componentData of Object.values(components)) {
      if (
        componentData.api.selector === selector &&
        selector.startsWith("fin-")
      ) {
        return componentData;
      }
    }

    return null;
  }

  private generateFincloudComponentSnippet(api: any): string {
    const inputs = api.inputs.filter((input: any) => input.required);

    if (inputs.length === 0) {
      return `<${api.selector}></${api.selector}>`;
    }

    const inputAttrs = inputs
      .map((input: any, index: number) => `${input.name}="$${index + 1}"`)
      .join(" ");

    return `<${api.selector} ${inputAttrs}></${api.selector}>`;
  }

  private calculateFincloudConfidence(match: string, query: string): number {
    // Higher confidence for exact Fincloud component matches
    if (match === query) return 1.0;
    if (match.toLowerCase() === query.toLowerCase()) return 0.95;

    // Boost confidence for Fincloud components
    if (match.startsWith("Fin") && query.toLowerCase().includes("fin"))
      return 0.9;
    if (match.includes(query)) return 0.8;
    if (match.toLowerCase().includes(query.toLowerCase())) return 0.7;

    // Levenshtein distance based confidence
    const distance = this.levenshteinDistance(
      match.toLowerCase(),
      query.toLowerCase()
    );
    const maxLength = Math.max(match.length, query.length);
    return Math.max(0, 1 - distance / maxLength);
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1)
      .fill(null)
      .map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }
}
