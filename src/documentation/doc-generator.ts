import {
  ComponentDocumentation,
  APIReference,
  CodeExample,
  ComponentMetadata,
  ComponentAPI,
} from "../types/schema.js";
import { FincloudUIIndexer } from "../indexers/component-indexer.js";

export interface DocumentationOptions {
  includeExamples?: boolean;
  includeApi?: boolean;
  includeRelated?: boolean;
  format?: "json" | "markdown" | "html";
}

export class DocumentationGenerator {
  private indexer: FincloudUIIndexer;

  constructor(indexer: FincloudUIIndexer) {
    this.indexer = indexer;
  }

  async generateDocumentation(
    componentName: string,
    options: DocumentationOptions = {}
  ): Promise<ComponentDocumentation> {
    const {
      includeExamples = true,
      includeApi = true,
      includeRelated = true,
    } = options;

    const componentRegistry = this.indexer.getComponentRegistry();
    const componentData = componentRegistry[componentName];

    if (!componentData) {
      throw new Error(`Component '${componentName}' not found`);
    }

    const overview = this.generateOverview(componentData.metadata);
    const api = includeApi
      ? this.generateAPIReference(componentData.api)
      : this.getEmptyAPIReference();
    const examples = includeExamples
      ? await this.generateUsageExamples(componentData)
      : [];
    const relatedComponents = includeRelated
      ? componentData.relationships.relatedComponents
      : [];

    return {
      overview,
      api,
      examples,
      relatedComponents,
    };
  }

  private generateOverview(metadata: ComponentMetadata): string {
    let overview = `# ${metadata.name}\n\n`;

    if (metadata.documentation) {
      overview += `${metadata.documentation}\n\n`;
    }

    overview += `## Component Information\n\n`;
    overview += `- **Selector**: \`${metadata.selector}\`\n`;
    overview += `- **Module**: \`${metadata.exportPath}\`\n`;
    overview += `- **Category**: ${metadata.category}\n`;

    if (metadata.tags.length > 0) {
      overview += `- **Tags**: ${metadata.tags
        .map((tag) => `\`${tag}\``)
        .join(", ")}\n`;
    }

    if (metadata.deprecated) {
      overview += `\n> ⚠️ **Deprecated**: This component is deprecated and may be removed in future versions.\n`;
    }

    if (metadata.since) {
      overview += `- **Since**: v${metadata.since}\n`;
    }

    if (metadata.storybookUrl) {
      overview += `- **Storybook**: [View Examples](${metadata.storybookUrl})\n`;
    }

    overview += `\n## Installation\n\n`;
    overview += `\`\`\`bash\nnpm install @fincloud/ui\n\`\`\`\n\n`;
    overview += `\`\`\`typescript\nimport { ${metadata.name} } from '${metadata.exportPath}';\n\`\`\`\n\n`;

    return overview;
  }

  private generateAPIReference(api: ComponentAPI): APIReference {
    return {
      selector: api.selector,
      inputs: api.inputs.map((input) => ({
        ...input,
        examples: this.generatePropertyExamples(input.name, input.type),
      })),
      outputs: api.outputs,
      methods: api.methods.filter((method) => method.isPublic),
      contentProjection: api.contentProjection,
    };
  }

  private async generateUsageExamples(
    componentData: any
  ): Promise<CodeExample[]> {
    const examples: CodeExample[] = [];

    // Use existing examples from the indexer
    examples.push(...componentData.examples);

    // Generate basic usage example if none exist
    if (examples.length === 0) {
      examples.push(await this.generateBasicExample(componentData));
    }

    // Generate advanced examples based on component complexity
    if (componentData.api.inputs.length > 3) {
      examples.push(await this.generateAdvancedExample(componentData));
    }

    // Generate form integration example if component has form-related inputs
    if (this.hasFormInputs(componentData.api)) {
      examples.push(await this.generateFormExample(componentData));
    }

    return examples;
  }

  private async generateBasicExample(componentData: any): Promise<CodeExample> {
    const { metadata, api } = componentData;
    const requiredInputs = api.inputs.filter((input: any) => input.required);

    let template = `<${api.selector}`;

    // Add required inputs with example values
    for (const input of requiredInputs) {
      const exampleValue = this.generateExampleValue(input.type);
      template += `\n  ${input.name}="${exampleValue}"`;
    }

    template += `>\n</${api.selector}>`;

    const typescript = this.generateComponentClass(metadata.name);

    return {
      title: `${metadata.name} - Basic Usage`,
      description: `Basic example showing how to use the ${metadata.name} component`,
      code: {
        template,
        typescript,
      },
    };
  }

  private async generateAdvancedExample(
    componentData: any
  ): Promise<CodeExample> {
    const { metadata, api } = componentData;

    let template = `<${api.selector}`;

    // Add multiple inputs with realistic values
    const inputsToShow = api.inputs.slice(0, 5); // Show up to 5 inputs
    for (const input of inputsToShow) {
      const exampleValue = this.generateAdvancedExampleValue(
        input.type,
        input.name
      );
      template += `\n  [${input.name}]="${exampleValue}"`;
    }

    // Add event handlers if outputs exist
    if (api.outputs.length > 0) {
      const firstOutput = api.outputs[0];
      template += `\n  (${firstOutput.name})="handle${this.capitalize(
        firstOutput.name
      )}($event)"`;
    }

    template += `>\n</${api.selector}>`;

    const typescript = this.generateAdvancedComponentClass(metadata.name, api);

    return {
      title: `${metadata.name} - Advanced Usage`,
      description: `Advanced example with multiple properties and event handling`,
      code: {
        template,
        typescript,
      },
    };
  }

  private async generateFormExample(componentData: any): Promise<CodeExample> {
    const { metadata, api } = componentData;

    const template = `<form [formGroup]="exampleForm">
  <${api.selector}
    formControlName="${this.camelCase(metadata.name.replace("Component", ""))}"
    [required]="true">
  </${api.selector}>
</form>`;

    const typescript = `import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-example',
  template: \`${template}\`,
})
export class ExampleComponent {
  exampleForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.exampleForm = this.fb.group({
      ${this.camelCase(
        metadata.name.replace("Component", "")
      )}: ['', Validators.required],
    });
  }
}`;

    return {
      title: `${metadata.name} - Form Integration`,
      description: `Example showing integration with Angular Reactive Forms`,
      code: {
        template,
        typescript,
      },
    };
  }

  // Helper methods

  private generatePropertyExamples(
    propertyName: string,
    propertyType: string
  ): string[] {
    const examples: string[] = [];

    switch (propertyType.toLowerCase()) {
      case "string":
        examples.push(`"Example ${propertyName}"`);
        break;
      case "number":
        examples.push("42");
        break;
      case "boolean":
        examples.push("true", "false");
        break;
      case "array":
        examples.push('["item1", "item2", "item3"]');
        break;
      default:
        examples.push(`// ${propertyType} value`);
    }

    return examples;
  }

  private generateExampleValue(type: string): string {
    switch (type.toLowerCase()) {
      case "string":
        return "Example text";
      case "number":
        return "42";
      case "boolean":
        return "true";
      default:
        return "value";
    }
  }

  private generateAdvancedExampleValue(
    type: string,
    propertyName: string
  ): string {
    if (propertyName.toLowerCase().includes("disabled")) {
      return "isDisabled";
    }
    if (propertyName.toLowerCase().includes("loading")) {
      return "isLoading";
    }
    if (propertyName.toLowerCase().includes("data")) {
      return "componentData";
    }

    return this.generateExampleValue(type);
  }

  private generateComponentClass(componentName: string): string {
    return `import { Component } from '@angular/core';

@Component({
  selector: 'app-example',
  templateUrl: './example.component.html',
})
export class ExampleComponent {
  // Component logic here
}`;
  }

  private generateAdvancedComponentClass(
    componentName: string,
    api: ComponentAPI
  ): string {
    let classContent = `import { Component } from '@angular/core';\nimport { ${componentName} } from '${this.getImportPath(
      componentName
    )}';\n\n@Component({\n  selector: 'app-example',\n  templateUrl: './example.component.html',\n})\nexport class ExampleComponent {\n`;

    // Add properties for inputs
    for (const input of api.inputs.slice(0, 3)) {
      const propertyName = this.camelCase(input.name);
      classContent += `  ${propertyName} = ${this.generateExampleValue(
        input.type
      )};\n`;
    }

    // Add event handlers for outputs
    for (const output of api.outputs.slice(0, 2)) {
      const handlerName = `handle${this.capitalize(output.name)}`;
      classContent += `\n  ${handlerName}(event: any): void {\n    console.log('${output.name} event:', event);\n  }\n`;
    }

    classContent += "}";
    return classContent;
  }

  private hasFormInputs(api: ComponentAPI): boolean {
    const formRelatedNames = [
      "value",
      "formControl",
      "formControlName",
      "disabled",
      "required",
    ];
    return api.inputs.some((input) =>
      formRelatedNames.some((name) => input.name.toLowerCase().includes(name))
    );
  }

  private getImportPath(componentName: string): string {
    const metadata = this.indexer.getComponentMetadata(componentName);
    return metadata ? metadata.exportPath : "@fincloud/ui";
  }

  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  private camelCase(str: string): string {
    return str.charAt(0).toLowerCase() + str.slice(1);
  }

  private getEmptyAPIReference(): APIReference {
    return {
      selector: "",
      inputs: [],
      outputs: [],
      methods: [],
      contentProjection: [],
    };
  }

  async generateMarkdownDocumentation(componentName: string): Promise<string> {
    const documentation = await this.generateDocumentation(componentName, {
      format: "markdown",
    });

    let markdown = documentation.overview + "\n";

    // API Reference
    if (
      documentation.api.inputs.length > 0 ||
      documentation.api.outputs.length > 0
    ) {
      markdown += "## API Reference\n\n";

      if (documentation.api.inputs.length > 0) {
        markdown += "### Inputs\n\n";
        markdown += "| Name | Type | Required | Description |\n";
        markdown += "|------|------|----------|-------------|\n";

        for (const input of documentation.api.inputs) {
          markdown += `| \`${input.name}\` | \`${input.type}\` | ${
            input.required ? "✓" : ""
          } | ${input.documentation} |\n`;
        }
        markdown += "\n";
      }

      if (documentation.api.outputs.length > 0) {
        markdown += "### Outputs\n\n";
        markdown += "| Name | Type | Description |\n";
        markdown += "|------|------|-------------|\n";

        for (const output of documentation.api.outputs) {
          markdown += `| \`${output.name}\` | \`${output.type}\` | ${output.documentation} |\n`;
        }
        markdown += "\n";
      }
    }

    // Examples
    if (documentation.examples.length > 0) {
      markdown += "## Examples\n\n";

      for (const example of documentation.examples) {
        markdown += `### ${example.title}\n\n`;
        markdown += `${example.description}\n\n`;

        if (example.code.typescript) {
          markdown +=
            "**TypeScript:**\n```typescript\n" +
            example.code.typescript +
            "\n```\n\n";
        }

        if (example.code.template) {
          markdown +=
            "**Template:**\n```html\n" + example.code.template + "\n```\n\n";
        }
      }
    }

    // Related Components
    if (documentation.relatedComponents.length > 0) {
      markdown += "## Related Components\n\n";
      for (const related of documentation.relatedComponents) {
        markdown += `- ${related}\n`;
      }
      markdown += "\n";
    }

    return markdown;
  }
}
