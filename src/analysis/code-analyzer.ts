import { readFile } from "fs/promises";
import { existsSync } from "fs";

import { AnalysisResult } from "../types/schema.js";
import { FincloudUIIndexer } from "../indexers/component-indexer.js";

export interface AnalysisOptions {
  filePath?: string;
  code?: string;
}

export class CodeAnalyzer {
  private indexer: FincloudUIIndexer;

  constructor(indexer: FincloudUIIndexer) {
    this.indexer = indexer;
  }

  async analyzeCode(options: AnalysisOptions): Promise<AnalysisResult[]> {
    const { filePath, code } = options;

    let sourceCode: string;
    if (code) {
      sourceCode = code;
    } else if (filePath && existsSync(filePath)) {
      sourceCode = await readFile(filePath, "utf-8");
    } else {
      throw new Error("Either filePath or code must be provided");
    }

    // Only perform Fincloud-specific analysis
    return this.analyzeFincloudUsage(sourceCode);
  }

  private analyzeFincloudUsage(code: string): AnalysisResult[] {
    const results: AnalysisResult[] = [];

    // Check for deprecated Fincloud components using component registry
    const componentRegistry = this.indexer.getComponentRegistry();

    Object.entries(componentRegistry).forEach(([, component]) => {
      if (
        component.metadata.deprecated &&
        code.includes(component.metadata.selector)
      ) {
        const alternatives = component.relationships.alternatives;
        const suggestion =
          alternatives.length > 0
            ? `Migrate to ${alternatives[0]}`
            : "Migrate to the newer version of this component";

        results.push({
          type: "warning",
          message: `Deprecated component '${component.metadata.selector}' detected`,
          suggestion,
        });
      }
    });

    // Check for proper Fincloud import patterns
    this.checkFincloudImportPatterns(code, results);

    return results;
  }

  private checkFincloudImportPatterns(
    code: string,
    results: AnalysisResult[]
  ): void {
    // Check for barrel imports (discouraged)
    if (
      code.includes("from '@fincloud/ui'") &&
      !code.includes("from '@fincloud/ui/")
    ) {
      results.push({
        type: "info",
        message:
          "Consider using specific module imports instead of barrel imports",
        suggestion:
          "Use '@fincloud/ui/[component's entry point name]' or '@fincloud/ui/utils' for better tree-shaking",
      });
    }

    // Check for missing Fincloud imports when using Fincloud components
    const fincloudComponentPattern = /<fin-[\w-]+/g;
    const fincloudComponents = code.match(fincloudComponentPattern);

    if (fincloudComponents && !code.includes("@fincloud/ui")) {
      results.push({
        type: "error",
        message: "Fincloud components detected but no Fincloud imports found",
        suggestion: "Add appropriate imports from '@fincloud/ui'",
      });
    }
  }
}
