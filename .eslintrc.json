{"env": {"es2022": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-arrow-callback": "error"}, "ignorePatterns": ["dist/**/*", "node_modules/**/*", "**/*.d.ts"]}