# Source files
src/
tsconfig.json
jest.config.js

# Development files
.mcp-cache/
coverage/
*.log
.env*

# IDE files
.vscode/
.idea/

# Git files
.git/
.gitignore

# CI files
.github/
.travis.yml
.circleci/

# Test files
**/*.test.ts
**/*.spec.ts
__tests__/

# Development configs
.eslintrc*
.prettierrc*

# Development-only documentation and scripts
INTEGRATION.md
TEAM_SETUP.md
examples/
scripts/
setup-mcp.js

# Keep essential files for package users
!README.md
!LICENSE
!package.json
!dist/
!fincloud-ui-mcp.config.json